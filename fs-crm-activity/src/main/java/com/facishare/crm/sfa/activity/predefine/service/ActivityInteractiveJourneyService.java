package com.facishare.crm.sfa.activity.predefine.service;


import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.activity.predefine.service.model.ActivityInteractiveJourneyModel;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Service
@ServiceModule("interactive_journey")
@AllArgsConstructor
@Slf4j
public class ActivityInteractiveJourneyService {
    private static final String SUMMARY = "summary";
    private final ServiceFacade serviceFacade;
    private final ActivityUserService activityUserService;

    @ServiceMethod("query")
    public ActivityInteractiveJourneyModel.Result query(ServiceContext context, ActivityInteractiveJourneyModel.Arg arg) {
        ActivityInteractiveJourneyModel.Result result = new ActivityInteractiveJourneyModel.Result();
        fillInteractiveScore(context, arg, result);
        fillProfile(context, arg, result);
        return result;
    }

    private void fillInteractiveScore(ServiceContext context, ActivityInteractiveJourneyModel.Arg arg, ActivityInteractiveJourneyModel.Result result) {
        ZoneId zoneId = Optional.ofNullable(context.getTimeZone()).orElse(ZoneId.systemDefault());
        SearchTemplateQuery query = buildActiveRecordSearchTemplateQuery(arg);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.addFilter(Operator.GTE, DBRecord.CREATE_TIME, String.valueOf(timestampBefore6Weeks(Instant.now(), zoneId)));
        queryExt.setLimit(420); // 42天*10个
        queryExt.setOffset(0);
        List<IObjectData> dataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(context.getUser(), Utils.ACTIVE_RECORD_API_NAME, query, Lists.newArrayList(DBRecord.ID, DBRecord.CREATE_TIME, "interactive_types", "interactive_scores")).getData();
        Map<LocalDate, List<ObjectDataDocument>> collect = dataList.stream().collect(Collectors.groupingBy(data -> toLocalDate(data.getCreateTime(), zoneId), TreeMap::new, Collectors.mapping(ObjectDataDocument::of, Collectors.toList())));
        result.setInteractiveScoreList(collect.values());
    }

    private void fillProfile(ServiceContext context, ActivityInteractiveJourneyModel.Arg arg, ActivityInteractiveJourneyModel.Result result) {
        User user = context.getUser();
        List<String> methodologyIds = queryFlowMethodologyIds(user);
        if (methodologyIds.isEmpty()) {
            log.warn("No methodology id found");
            return;
        }
        ZoneId zoneId = Optional.ofNullable(context.getTimeZone()).orElse(ZoneId.systemDefault());
        SearchTemplateQuery query = buildProfileSearchTemplateQuery(arg);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.addFilter(Operator.IN, "methodology_id", methodologyIds)
                .addFilter(Operator.GTE, DBRecord.CREATE_TIME, String.valueOf(timestampBefore6Weeks(Instant.now(), zoneId)));
        queryExt.setLimit(20); // 理论上每周一个画像
        queryExt.setPermissionType(0);
        queryExt.setOrders(Collections.singletonList(new OrderBy(DBRecord.CREATE_TIME, false)));
        List<IObjectData> profileDataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "ProfileObj", query, Lists.newArrayList(DBRecord.ID, DBRecord.CREATE_TIME, "integrated_score")).getData();
        if (profileDataList.isEmpty()) {
            log.info("No profile found");
            return;
        }
        List<String> profileIds = new ArrayList<>();
        List<ObjectDataDocument> profileDocList = new ArrayList<>();
        for (IObjectData p : profileDataList) {
            profileIds.add(p.getId());
            profileDocList.add(ObjectDataDocument.of(p));
        }
        result.setProfileIntegratedScoreList(profileDocList);
        List<IObjectData> prefileItemScoreList = queryProfileItemScore(user, profileIds);
        result.setPrefileItemScoreList(prefileItemScoreList.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        if (arg.isShowLatestProfileItems() && !prefileItemScoreList.isEmpty()) {
            IObjectData latestProfileScore = null;
            for (IObjectData p : prefileItemScoreList) {
                if (latestProfileScore == null) {
                    latestProfileScore = p;
                } else {
                    if (latestProfileScore.getCreateTime() < p.getCreateTime()) {
                        latestProfileScore = p;
                    }
                }
            }
            fillProfileItemScoreSummary(user, latestProfileScore);
            result.setLatestProfileItemScore(ObjectDataDocument.of(latestProfileScore));
            queryLastestProfileAdvice(user, profileIds)
                    .ifPresent(advice -> result.setLatestProfileAdvice(ObjectDataDocument.of(advice)));
        }
    }

    @ServiceMethod("query_interactive_score_detail")
    public ActivityInteractiveJourneyModel.Result queryInteractiveScoreDetail(ServiceContext context, ActivityInteractiveJourneyModel.Arg arg) {
        ActivityInteractiveJourneyModel.Result result = new ActivityInteractiveJourneyModel.Result();
        SearchTemplateQuery query = buildActiveRecordSearchTemplateQuery(arg);
        List<IObjectData> dataList = serviceFacade.findBySearchQueryIgnoreAll(context.getUser(), Utils.ACTIVE_RECORD_API_NAME, query).getData();
        if (dataList.isEmpty()) {
            return result;
        }
        IObjectData data = dataList.get(0);
        List<IObjectData> activityUsers = activityUserService.getActivityUsersWithProfileImage(context.getTenantId(), data.getId());
        data.set("activity_users", activityUsers.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        result.setInteractiveScoreDetail(ObjectDataDocument.of(data));
        return result;
    }

    private long timestampBefore6Weeks(Instant now, ZoneId zoneId) {
        LocalDate beforeDate = now.atZone(zoneId).toLocalDate().minusWeeks(6);
        return LocalDateTime.of(beforeDate, LocalTime.MIDNIGHT).atZone(zoneId).toInstant().toEpochMilli();
    }

    private static LocalDate toLocalDate(long timestamp, ZoneId zoneId) {
        return Instant.ofEpochMilli(timestamp).atZone(zoneId).toLocalDate();
    }

    private SearchTemplateQuery buildActiveRecordSearchTemplateQuery(ActivityInteractiveJourneyModel.Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        if (arg.getOpportunityId() != null) {
            queryExt.addFilter(Operator.EQ, "new_opportunity_id", arg.getOpportunityId());
        }
        if (arg.getAccountId() != null) {
            queryExt.addFilter(Operator.EQ, "account_id", arg.getAccountId());
        }
        if (arg.getActiveRecordId() != null) {
            queryExt.addFilter(Operator.EQ, DBRecord.ID, arg.getActiveRecordId());
        }
        return query;
    }

    private SearchTemplateQuery buildProfileSearchTemplateQuery(ActivityInteractiveJourneyModel.Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        if (arg.getOpportunityId() != null) {
            queryExt.addFilter(Operator.EQ, "opportunity_id", arg.getOpportunityId());
        }
        if (arg.getAccountId() != null) {
            queryExt.addFilter(Operator.EQ, "account_id", arg.getAccountId());
        }
        return query;
    }

    private List<String> queryFlowMethodologyIds(User user) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.addFilter(Operator.EQ, "type", "flow")
                .addFilter(Operator.EQ, "life_status", "normal")
                .addFilter(Operator.EQ, "status", "published");
        queryExt.setLimit(100);
        queryExt.setPermissionType(0);
        return serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "MethodologyObj", query, Collections.singletonList(DBRecord.ID)).getData().stream().map(IObjectData::getId).collect(Collectors.toList());
    }

    private List<IObjectData> queryProfileItemScore(User user, List<String> profileIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        // 客户关系纬度写死67f8c4985631ea000139ae66
        queryExt.addFilter(Operator.IN, "profile_id", profileIds)
                .addFilter(Operator.EQ, "feature_dimension_id", "67f8c4985631ea000139ae66");
        queryExt.setPermissionType(0);
        return serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "ProfileItemScoreObj", query, Lists.newArrayList(DBRecord.ID, DBRecord.CREATE_TIME, "score")).getData();
    }

    private void fillProfileItemScoreSummary(User user, IObjectData data) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.addFilter(Operator.EQ, DBRecord.ID, data.getId());
        queryExt.setPermissionType(0);
        List<IObjectData> list = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "ProfileItemScoreObj", query, Lists.newArrayList(SUMMARY)).getData();
        list.stream().findFirst().ifPresent(first -> data.set(SUMMARY, first.get(SUMMARY)));
    }

    private Optional<IObjectData> queryLastestProfileAdvice(User user, List<String> profileIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.addFilter(Operator.IN, "profile_id", profileIds)
                .addFilter(Operator.EQ, "feature_dimension_id", "67f8c4985631ea000139ae66");
        queryExt.setOrders(Collections.singletonList(new OrderBy(DBRecord.CREATE_TIME, false)));
        queryExt.setLimit(1);
        queryExt.setPermissionType(0);
        List<IObjectData> list = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, "ProfileAdviceObj", query, Lists.newArrayList(DBRecord.ID, DBRecord.CREATE_TIME, "advice")).getData();
        return list.stream().findFirst();
    }
}
