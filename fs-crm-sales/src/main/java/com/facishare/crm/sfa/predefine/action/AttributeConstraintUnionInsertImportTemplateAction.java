package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.AttributeConstaintLinesConstants;
import com.facishare.crm.sfa.utilities.util.i18n.AttributeConstraintI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class AttributeConstraintUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    @Override
    protected void customDetailHeader(List<IFieldDescribe> detailFieldList) {
        super.customDetailHeader(detailFieldList);
        Map<String, Object> map = Maps.newHashMap();
        map.put("label", I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAINT_IMPORT_NODE_ID));
        map.put("api_name", AttributeConstaintLinesConstants.FIELD_NODE_TEMP_ID);
        IFieldDescribe field = ImportExportExt.createField(map, "TEMP_ID_MARK", true);
        detailFieldList.add(1, field);
    }

    /**
     * 对从对象的字段进行重新排序
     * @param validFieldList
     * @return
     */
    @Override
    protected List<IFieldDescribe> sortHeader(List<IFieldDescribe> validFieldList) {
        List<IFieldDescribe> list = super.sortHeader(validFieldList);
        List<IFieldDescribe> resultList = Lists.newArrayList();
        List<IFieldDescribe> tempList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(list) && Objects.equals(AttributeConstaintLinesConstants.DESC_API_NAME, list.get(0).getDescribeApiName())) {
            IFieldDescribe parentIdField = null;
            IFieldDescribe rootNodeField = null;
            IFieldDescribe tempIdField = null;
            for (IFieldDescribe field : list) {
                if(AttributeConstaintLinesConstants.FIELD_PARENT_ID.equals(field.getApiName())) {
                    parentIdField = field;
                } else if(AttributeConstaintLinesConstants.FIELD_ROOT_NODE_ID.equals(field.getApiName())) {
                    rootNodeField = field;
                } else if(AttributeConstaintLinesConstants.FIELD_NODE_TEMP_ID.equals(field.getApiName())) {
                    tempIdField = field;
                } else {
                    tempList.add(field);
                }
            }
            resultList.add(rootNodeField);
            resultList.add(tempIdField);
            resultList.add(parentIdField);
            resultList.addAll(tempList);
            return resultList;
        } else {
            return list;
        }
    }

    @Override
    protected String getFieldSampleValue(IFieldDescribe field) {
        if(Objects.equals(AttributeConstaintLinesConstants.FIELD_NODE_TYPE, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAINT_IMPORT_NODE_TYPE);
        } else if(Objects.equals(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_ID, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_LABEL);
        } else if(Objects.equals(AttributeConstaintLinesConstants.FIELD_ATTRIBUTE_VALUE_IDS, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_VALUE_LABEL);
        } else if(Objects.equals(AttributeConstaintLinesConstants.FIELD_DEFAULT_ATTR_VALUES_IDS, field.getApiName())) {
            return I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAIN_IMPORT_ATTRIBUTE_DEFAULT_VALUE_LABEL);
        } else {
            return super.getFieldSampleValue(field);
        }
    }
}
