package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.AttributeConstaintLinesConstants;
import com.facishare.crm.sfa.utilities.util.i18n.AttributeConstraintI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class AttributeConstraintImportViewController extends StandardImportViewController {

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (StringUtils.equals(describe.getApiName(), AttributeConstaintLinesConstants.DESC_API_NAME) && arg.getImportType() == IMPORT_TYPE_ADD) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("label", I18N.text(AttributeConstraintI18NKeyUtil.ATTRIBUTE_CONSTRAINT_IMPORT_NODE_ID));
            map.put("api_name", AttributeConstaintLinesConstants.FIELD_NODE_TEMP_ID);
            IFieldDescribe field = ImportExportExt.createField(map, "TEMP_ID_MARK", true);
            fieldDescribes.add(1, field);
        }
    }


}
