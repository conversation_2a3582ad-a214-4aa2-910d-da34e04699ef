package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFARoleUtil;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/11 9:31 上午
 */
public class SalesOrderWebDetailController extends SFAWebDetailController {

    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private final FunctionPrivilegeService functionPrivilegeService = (FunctionPrivilegeService) SpringUtil.getContext().getBean("functionPrivilegeService");
    private final InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);
    protected BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final String SALES_ORDER_INVOICE_APP_MULTI_RELATED_LIST = "sales_order_invoice_app_multi_related_list";
    private static final String MULTI_SALES_ORDER_INVOICE_JSON = "{\n" +
            "    \"buttons\": [],\n" +
            "    \"api_name\": \"sales_order_invoice_app_multi_related_list\",\n" +
            "    \"field_api_name\": \"order_invoice_related_list_yxy\",\n" +
            "    \"is_hidden\": false,\n" +
            "    \"header\": \"开票申请\",\n" +
            "    \"type\": \"relatedlist\",\n" +
            "    \"ref_object_api_name\": \"InvoiceApplicationObj\",\n" +
            "    \"related_list_name\": \"sales_order_invoice_app_multi_related_list\",\n" +
            "    \"order\": 999\n" +
            "}";

    @Override
    protected void before(Arg arg) {
        multiUnitService.validateMobileClientVersion(controllerContext.getRequestContext());
        super.before(arg);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        SalesOrderUtil.handlePaymentMoneyToConfirm(controllerContext.getTenantId(),
                Lists.newArrayList(result.getData().toObjectData()));
        SalesOrderUtil.handleBillMoneyToConfirm(controllerContext.getTenantId(),
                Lists.newArrayList(result.getData().toObjectData()));
        SalesOrderUtil.handlePolicyRule(controllerContext.getTenantId(), Lists.newArrayList(result.getData().toObjectData()));
        SalesOrderUtil.handleRangeRebateRule(controllerContext.getTenantId(), Lists.newArrayList(result.getData().toObjectData()));
        if (Boolean.TRUE.equals(arg.getIncludeLayout())) {
            ILayout layout = new Layout(newResult.getLayout());
            checkDeliveryNotePrivilege(layout);
            //按条件过滤"变更"按钮
            filterChangeButton(layout, result.getData().toObjectData().getOwner());

            // 处理开票和订单的多对多关系
            if (invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
                addInvoiceAppLayoutStructure(layout);
                WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("InvoiceApplicationObj_order_id_related_list"));
            }
            layout = removeButton(layout);
            removeFieldsIfHasDhtOrderDeliveryAddress(layout);
            filterCloseOrderButton(layout,result);
            filterArButton(layout, result);
            newResult.setLayout(LayoutDocument.of(layout));
        }
        return newResult;
    }

    private void filterCloseOrderButton(ILayout layout,Result result) {
        boolean closeStatus = Optional.ofNullable(result.getData()).map(ObjectDataDocument::toObjectData).map(x -> x.get(SalesOrderConstants.SalesOrderField.CLOSE_STATUS.getApiName(), Boolean.class,false)).orElse(false);
        if (closeStatus) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLOSE_SALES_ORDER.getActionCode(),ObjectAction.UPDATE.getActionCode()));
        }
        ObjectDataDocument data = result.getData();
        if (Objects.nonNull(data)) {
            IObjectData iObjectData = data.toObjectData();
            String lifeStatus = iObjectData.get(ObjectLifeStatus.LIFE_STATUS_API_NAME,String.class);
            if (!Objects.equals(ObjectLifeStatus.NORMAL.getCode(),lifeStatus)) {
                WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLOSE_SALES_ORDER.getActionCode()));
            }
        }

        List<IButton> buttons = WebDetailLayout.of(layout).getButtons();
        if (CollectionUtils.notEmpty(buttons)) {
            boolean flag = false ;
            for (IButton x : buttons) {
                if (Objects.equals(x.getAction(), ObjectAction.UPDATE.getActionCode())) {
                    flag = true;
                }
            }
            if(!flag){
                WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLOSE_SALES_ORDER.getActionCode()));
            }
        }
    }

    private void filterArButton(ILayout layout,Result result) {
        IObjectData data = result.getData().toObjectData();
        BigDecimal arQuickRule = data.get("no_ar_tag_amount", BigDecimal.class, BigDecimal.ZERO);

        if (!bizConfigThreadLocalCacheService.isOpenArQuickRule(controllerContext.getTenantId())|| arQuickRule.compareTo(BigDecimal.ZERO) <= 0) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.AR_QUICK_CREATE.getActionCode()));
        }
    }

    private void filterChangeButton(ILayout layout, List<String> owners) {
        boolean isManagerOrFinance = false;
        if (!Strings.isNullOrEmpty(controllerContext.getUser().getUpstreamOwnerIdOrUserId()) && CollectionUtils.notEmpty(owners)) {
            int empId = Integer.parseInt(controllerContext.getUser().getUpstreamOwnerIdOrUserId());
            List<Integer> ownerIds = owners.stream().map(Integer::valueOf).collect(Collectors.toList());
            Map<String, Boolean> isOrderManagerOrFinance = SFARoleUtil.isOrderManagerOrFinance(controllerContext.getUser(), empId, ownerIds);
            if(CollectionUtils.notEmpty(isOrderManagerOrFinance)) {
                isManagerOrFinance = isOrderManagerOrFinance.values().stream().anyMatch(x->x.booleanValue());
            }
        }
        if(bizConfigThreadLocalCacheService.isOpenCoupon(controllerContext.getTenantId())
                || bizConfigThreadLocalCacheService.isOpenRebate(controllerContext.getTenantId())
                || bizConfigThreadLocalCacheService.isOpenPricePolicy(controllerContext.getTenantId())
                //没开并且不是管理员或财务管理员角色，需要屏蔽"变更"按钮
                || (!bizConfigThreadLocalCacheService.isEnlargeEditPrivilege(controllerContext.getTenantId()) && !isManagerOrFinance)
        ) {
            List<String> removeButtonActions = Lists.newArrayList(ObjectAction.CHANGE.getActionCode());
            WebDetailLayout.of(layout).removeButtonsByActionCode(removeButtonActions);
        }
    }

    private ILayout removeButton(ILayout layout) {
        List<String> removeButtonActions = Lists.newArrayList(ObjectAction.RECOVER.getActionCode());
        if(RequestUtil.isWebRequest()){
            removeButtonActions.add(ObjectAction.ONE_MORE_ORDER.getActionCode());
        }
        WebDetailLayout.of(layout).removeButtonsByActionCode(removeButtonActions);
        return layout;
    }


    private void addInvoiceAppLayoutStructure(ILayout layout) {
        try {
            Optional<String> exist = CollectionUtils.nullToEmpty(layout.getHiddenComponents()).stream().filter(x -> Objects.equals(SALES_ORDER_INVOICE_APP_MULTI_RELATED_LIST, x)).findFirst();
            if (exist.isPresent()) {
                return;
            }
            List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
            boolean flag = false;
            for (IComponent x : components) {
                if (Objects.equals(x.getName(), SALES_ORDER_INVOICE_APP_MULTI_RELATED_LIST)) {
                    x.setType("relatedlist");
                    x.set("field_api_name","order_invoice_related_list_yxy");
                    flag = true;
                    break;
                }
            }
            if (flag) {
                layout.setComponents(components);
            } else {
                if (!GrayUtil.needRemoveInvoiceApplicationComponent(controllerContext.getTenantId())) {
                    IComponent multiComponent = ComponentFactory.newInstance(MULTI_SALES_ORDER_INVOICE_JSON);
                    multiComponent.setHeader(I18N.text("InvoiceApplicationObj.field.order_id.reference_label"));
                    WebDetailLayout.of(layout).addComponents(Lists.newArrayList(multiComponent), 9999);
                }
            }
        } catch (Exception e) {
            log.warn("set component error", e);
        }
    }

    private void checkDeliveryNotePrivilege(ILayout layout) {
        try {
            List<IComponent> components1 = layout.getComponents();
            components1.forEach(x -> {
                List<IButton> buttonList = x.getButtons();
                if (CollectionUtils.notEmpty(buttonList)) {
                    boolean removed = buttonList.removeIf(button -> {
                        if (ObjectAction.ADD_DELIVERY_NOTE.getActionCode().equals(button.getAction())) {
                            if (needRemoveDeliveryButton()) {
                                return true;
                            }
                            List<String> actionCodes = Lists.newArrayList(ObjectAction.CREATE.getActionCode());
                            Map<String, Boolean> funPrivilegeMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(),
                                    Utils.DELIVERY_NOTE_API_NAME, actionCodes);
                            Boolean hasPrivilege = funPrivilegeMap.getOrDefault(ObjectAction.CREATE.getActionCode(), Boolean.FALSE);
                            return !Boolean.TRUE.equals(hasPrivilege);
                        }
                        return false;
                    });
                    if (removed) {
                        x.setButtons(buttonList);
                    }
                }
            });
        } catch (MetadataServiceException e) {
            log.warn("checkDeliveryNotePrivilege", e);
        }
    }

    private boolean needRemoveDeliveryButton() {
        return controllerContext.getUser().isOutUser() && GrayUtil.needRemoveDownstreamDeliveryButton(controllerContext.getTenantId());
    }

    /**
     * 布局中如果有 收货地址 组件，则表单布局中去掉三个（ship_to_add、ship_to_tel、ship_to_id）
     */
    private void removeFieldsIfHasDhtOrderDeliveryAddress(ILayout layout) {
        if (!hasDhtOrderDeliveryAddress(layout)) {
            return;
        }

        List<String> removeFieldApiNames = Lists.newArrayList("ship_to_add", "ship_to_tel", "ship_to_id");

        try {
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.empty(components)) {
                return;
            }

            for (IComponent component : components) {
                if (Objects.equals(component.getName(), "form_component")) {

                    FormComponent fc = (FormComponent) component;
                    List<IFieldSection> fields = fc.getFieldSections();
                    List<IFieldSection> newFields = Lists.newArrayList();

                    for (IFieldSection fieldSection : fields) {
                        boolean hasRemove = false;
                        List<IFormField> formFields = fieldSection.getFields();
                        Iterator iterator = formFields.iterator();
                        while (iterator.hasNext()) {
                            IFormField formField = (IFormField) iterator.next();
                            if (removeFieldApiNames.contains(formField.getFieldName())) {
                                iterator.remove();
                                hasRemove = true;
                            }
                        }
                        if (hasRemove) {
                            fieldSection.setFields(formFields);
                        }

                        /**
                         * 用户自建的，只有这3个字段，删完"form_fields": []是空的，把show_header 和 is_show 设为FALSE不行，所以把删了
                         {
                         "show_header":true,
                         "form_fields":[],
                         "api_name":"group_kCqo5__c",
                         "tab_index":"ltr",
                         "column":2,
                         "header":"",
                         "is_show":true
                         }
                         */
                        if (!CollectionUtils.empty(formFields) || Objects.equals(fieldSection.getName(), "base_field_section__c")) {
                            newFields.add(fieldSection);
                        }
                    }
                    fc.setFieldSections(newFields);

                    return;
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("removeFieldsIfHasDhtOrderDeliveryAddress ", e);
        }
    }

    /**
     * 是否有收货地址组件
     */
    private boolean hasDhtOrderDeliveryAddress(ILayout layout) {
        try {
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.empty(components)) {
                return false;
            }

            for (IComponent component : components) {
                if (Objects.equals(component.getName(), "dht_order_delivery_address")) {
                    return true;
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("hasDhtOrderDeliveryAddress ", e);
        }

        return false;
    }
}
