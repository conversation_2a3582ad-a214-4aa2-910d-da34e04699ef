package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.rest.TemplateApi;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.DescribeWithSimplifiedChineseService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.util.InitUtil;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableDetailObjConstants;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableNoteObjConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectValueMappingService;
import com.facishare.paas.appframework.metadata.JobScheduleService;
import com.facishare.paas.appframework.metadata.ObjectConvertRuleService;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.ObjectDataPermissionInfo;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutRuleService;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.facishare.crm.privilege.util.Constant.SYSTEM_OPT_USER_ID;

/**
 * 销售合同初始化
 * @IgnoreI18nFile
 */

@Component
@Slf4j
public class SaleContractModuleInitService extends AbstractModuleInitService {

    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    @Autowired
    private DataPrivilegeService dataPrivilegeService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    ILayoutService layoutService;
    @Autowired
    ILayoutRuleService layoutRuleService;
    @Autowired
    ObjectValueMappingService objectValueMappingService;
    @Autowired
    ConfigService configService;
    @Autowired
    private EnterpriseInitService enterpriseInitService;
    @Autowired
    private SaleContractModuleInitServiceExt saleContractModuleInitServiceExt;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;

    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;
    @Resource
    private TemplateApi templateApi;
    @Autowired
    JobScheduleService jobScheduleService;
    @Autowired
    private DescribeWithSimplifiedChineseService describeWithSimplifiedChineseService;

    private static final String FIELD_JSON_SALES_ORDER = "{\"sale_contract_id\":{\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"label\":\"销售合同\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"target_related_list_contract\",\"is_abstract\":null,\"field_num\":null,\"target_related_list_label\":\"销售订单\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"released\"}}";// ignoreI18n

    private static final String FIELD_JSON_SALES_ORDER_PRODUCT = "{\"sale_contract_line_id\":{\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"input_mode\":\"\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"label\":\"销售合同产品\",\"target_api_name\":\"SaleContractLineObj\",\"target_related_list_name\":\"target_related_list_contract_line\",\"is_abstract\":null,\"field_num\":null,\"target_related_list_label\":\"订单产品\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_line_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"released\"}}";// ignoreI18n

    private final String SALE_CONTRACT_ID = "{\"sale_contract_id\":{\"description\":\"销售合同编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同编号\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"related_list_sale_contract_order_payment\",\"target_related_list_label\":\"回款明细\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_id\",\"is_index_field\":true,\"help_text\":\"\",\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"related_order_payment_count\",\"field_values\":[\"0\"]},{\"value_type\":2,\"operator\":\"EQ\",\"field_name\":\"account_id\",\"field_values\":[\"$payment_id__r.account_id$\"]}]}],\"status\":\"new\"}}";// ignoreI18n
    private final String SALE_CONTRACT_LINE_ID = "{\"sale_contract_line_id\":{\"description\":\"销售合同明细编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同明细编号\",\"target_api_name\":\"SaleContractLineObj\",\"target_related_list_name\":\"related_list_contract_line_order_payment\",\"target_related_list_label\":\"回款明细\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_line_id\",\"is_index_field\":true,\"help_text\":\"\",\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"related_order_payment_count\",\"field_values\":[\"0\"]}]}],\"status\":\"new\"}}";// ignoreI18n
    private final String CONTRACT_RELATED_ORDER_PAYMENT_COUNT = "{\"contract_related_order_payment_count\":{\"return_type\":\"number\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":0,\"sub_object_describe_apiname\":\"OrderPaymentObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"field_api_name\":\"sale_contract_id\",\"is_index\":false,\"is_active\":true,\"count_type\":\"count\",\"count_field_api_name\":\"\",\"label\":\"关联回款\",\"count_to_zero\":true,\"api_name\":\"related_order_payment_count\",\"count_field_type\":\"\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"default_result\": \"d_zero\"}}";// ignoreI18n
    private final String CONTRACT_LINE_RELATED_ORDER_PAYMENT_COUNT = "{\"contract_line_related_order_payment_count\":{\"return_type\":\"number\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":0,\"sub_object_describe_apiname\":\"OrderPaymentObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"field_api_name\":\"sale_contract_line_id\",\"is_index\":false,\"is_active\":true,\"count_type\":\"count\",\"count_field_api_name\":\"\",\"label\":\"关联回款\",\"count_to_zero\":true,\"api_name\":\"related_order_payment_count\",\"count_field_type\":\"\",\"is_index_field\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"new\",\"default_result\": \"d_zero\"}}";// ignoreI18n
    private final String AR_TAG_AMOUNT = "{\"ar_tag_amount\":{\"return_type\":\"currency\",\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"AccountsReceivableDetailObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"field_api_name\":\"sale_contract_line_id\",\"is_index\":false,\"default_result\":\"d_zero\",\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"price_tax_amount\",\"label\":\"已立应收金额\",\"count_to_zero\":true,\"api_name\":\"ar_tag_amount\",\"count_field_type\":\"currency\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}}";// ignoreI18n
    private final String NO_AR_TAG_AMOUNT = "{\"no_ar_tag_amount\":{\"expression_type\":\"js\",\"return_type\":\"currency\",\"description\":\"\",\"is_unique\":false,\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"expression\":\"$subtotal$-$ar_tag_amount$\",\"label\":\"待立应收金额\",\"api_name\":\"no_ar_tag_amount\",\"help_text\":\"\",\"status\":\"released\"}}";// ignoreI18n
    private final String SALE_CONTRACT_ID_SETTLEMENT = "{\"sale_contract_id\":{\"description\":\"销售合同编号\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":2,\"operator\":\"EQ\",\"field_name\":\"account_id\",\"field_values\":[\"$account_id$\"]}]}],\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同编号\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"related_list_sale_contract_settlement\",\"target_related_list_label\":\"结算单\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"sale_contract_id\",\"is_index_field\":true,\"help_text\":\"\",\"status\":\"new\"}}";// ignoreI18n
    private final String SALE_CONTRACT_ID_FOR_AR = "{\"sale_contract_id\":{\"default_is_expression\":false,\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"target_related_list_sale_contract_receivable_note\",\"target_related_list_label\":\"应收单\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"api_name\":\"sale_contract_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}}";
    private final String SALE_CONTRACT_LINE_ID_FOR_AR = "{\"sale_contract_line_id\":{\"default_is_expression\":false,\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"销售合同明细\",\"target_api_name\":\"SaleContractLineObj\",\"target_related_list_name\":\"target_related_list_sale_contract_line_receivable_detail\",\"target_related_list_label\":\"应收单明细\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"api_name\":\"sale_contract_line_id\",\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"}}";
    private final String SALE_CONTRACT_AR_TAG_AMOUNT = "{\"ar_tag_amount\":{\"return_type\":\"currency\",\"describe_api_name\":\"SaleContractObj\",\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"SaleContractLineObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"label_r\":\"待立应收金额\",\"field_api_name\":\"order_id\",\"is_index\":true,\"default_result\":\"d_zero\",\"is_active\":true,\"is_encrypted\":false,\"count_type\":\"sum\",\"count_field_api_name\":\"no_ar_tag_amount\",\"label\":\"待立应收金额\",\"is_need_convert\":false,\"count_to_zero\":true,\"api_name\":\"no_ar_tag_amount\",\"count_field_type\":\"currency\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}}";
    private final String SALE_CONTRACT_NO_AR_TAG_AMOUNT = "{\"no_ar_tag_amount\":{\"return_type\":\"currency\",\"describe_api_name\":\"SaleContract\",\"auto_adapt_places\":false,\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"SaleContractLineObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"label_r\":\"已立应收金额\",\"field_api_name\":\"order_id\",\"is_index\":true,\"default_result\":\"d_zero\",\"is_active\":true,\"is_encrypted\":false,\"count_type\":\"sum\",\"count_field_api_name\":\"ar_tag_amount\",\"label\":\"已立应收金额\",\"is_need_convert\":false,\"count_to_zero\":true,\"api_name\":\"ar_tag_amount\",\"count_field_type\":\"currency\",\"is_index_field\":false,\"is_show_mask\":false,\"round_mode\":4,\"help_text\":\"\",\"status\":\"released\"}}";

    @Override
    public String getModuleCode() {
        return IModuleInitService.SALE_CONTRACT;
    }

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        if (SFAConfigUtil.isConfigOpen(tenantId, IModuleInitService.SALE_CONTRACT)) {
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                    .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                    ).build();
        }
        User user = new User(tenantId, userId);
        long startTime = System.currentTimeMillis();
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "#initModule");
        TraceContext traceContext = TraceContext.get();
        String traceId = traceContext.getTraceId();
        Map<String, IObjectDescribe> descMap = new HashMap<>();
        RequestContext context = RequestContextManager.getContext();
        List<String> apiNames = Lists.newArrayList(
                SFAPreDefineObject.SaleContract.getApiName(), SFAPreDefineObject.SaleContractLine.getApiName()
        );
        try {
            boolean isOpenMulti = bizConfigThreadLocalCacheService.isMultipleUnit(user.getTenantId());
            init(user, descMap, stopWatch, apiNames);
            initPrivilegeRelate(apiNames, user, context, DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue(), null);
            addField2OtherObj(user);
            initObjectMappingRule();
            configureBusinessAccordingToSwitches(user.getTenantId(), descMap, stopWatch);
            syncHandle(user,isOpenMulti);
            stopWatch.lap("syncHandle");
            sendMqAfterInitModuleSuccess(tenantId);
            sendAuditLog(tenantId, userId, traceId.concat(":").concat("success"), SFAPreDefineObject.SaleContract.getApiName(), startTime);
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                    .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                    ).build();
        } catch (Exception e) {
            sendAuditLog(tenantId, userId, traceId.concat(":").concat(e.toString()), SFAPreDefineObject.SaleContract.getApiName(), startTime);
            log.error("init {} error :{}", IModuleInitService.SALE_CONTRACT, e.getMessage(), e);
            throw new MetaDataBusinessException(e.getMessage());
        } finally {
            stopWatch.logSlow(500);
        }
    }

    private void configureBusinessAccordingToSwitches(String tenantId, Map<String, IObjectDescribe> descMap, StopWatch stopWatch) throws MetadataServiceException {
        saleContractModuleInitServiceExt.handle(tenantId, descMap, stopWatch);
    }

    private void syncHandle(User user, boolean isOpenMulti) {
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(this::initObjectConvertRule);
        parallelTask.submit(() -> initPrintTemplate(user.getTenantId()));
        parallelTask.submit(() -> {
            if (isOpenMulti) {
                //刷映射规则
                ModuleInitLayoutUtil.refreshMappingRuleForSaleContract(user);
            }
        });
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("bom fillData parallelTask error: tenantId:{}", user.getTenantId(), e);
        }
    }

    /**
     * @desc 预制打印模板步骤：
     * 1、fs-crm-printconfig-665 -> print_support_oldApiName_ListInfo 添加配置
     * 2、调用templateApi.initTemplate接口
     * 3、在fs-crm-template项目里配置模板样式
     */
    private void initPrintTemplate(String tenantId) {
        Map headers = new HashMap();
        headers.put("x-tenant-id", tenantId);
        headers.put("x-user-id", User.SUPPER_ADMIN_USER_ID);
        headers.put("Content-Type", "application/json");

        Map pathMap = new HashMap();
        pathMap.put("tenantId", tenantId);

        Map queryMap = new HashMap();
        queryMap.put("initDescribeApiNames", Utils.SALE_CONTRACT_API_NAME);

        try {
            templateApi.initTemplate(pathMap, queryMap, tenantId, headers);
        } catch (Exception e) {
            log.error("SaleContractModuleInitService templateApi.init (pathMap:{}, queryMap:{}, headers:{}", pathMap, queryMap, headers, e);
        }
    }

    private void initObjectMappingRule(){
        ServiceContext serviceContext = new ServiceContext(RequestContextManager.getContext(), null, null);
        try {
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_quoteobj2salecontractobj__c");
            enterpriseInitService.initObjectMappingRule(serviceContext, "rule_salecontractobj2salesorderobj__c");
        } catch (Exception e) {
            try {
                Thread.sleep(3000);
                enterpriseInitService.initObjectMappingRule(serviceContext, "rule_quoteobj2salecontractobj__c");
                enterpriseInitService.initObjectMappingRule(serviceContext, "rule_salecontractobj2salesorderobj__c");
            } catch (Exception ex) {
                log.error("SaleContractModuleInitService initObjectMappingRule.init {}",serviceContext.getTenantId() , e);
            }
        }
    }

    private void initObjectConvertRule() {
        ServiceContext serviceContext = new ServiceContext(RequestContextManager.getContext(), null, null);
        try {
            if (objectConvertRuleService.supportConvertRule(serviceContext.getTenantId())) {
                enterpriseInitService.initObjectConvertRule(serviceContext, "rule_convert_quote_to_salecontract");
                enterpriseInitService.initObjectConvertRule(serviceContext, "rule_convert_salecontract_to_order");
            }
        } catch (Exception e) {
            log.error("initObjectConvertRule error :{}", e.getMessage(), e);
        }
    }

    private void addField2OtherObj(User user) throws MetadataServiceException {
        Map<String, IObjectDescribe> describeApiNameMap = describeWithSimplifiedChineseService.findByDescribeApiNameList(user, Lists.newArrayList(
                Utils.SALES_ORDER_API_NAME,
                Utils.SALES_ORDER_PRODUCT_API_NAME,
                Utils.ORDER_PAYMENT_API_NAME,
                Utils.SALE_CONTRACT_API_NAME,
                Utils.SALE_CONTRACT_LINE_API_NAME,
                AccountsReceivableNoteObjConstants.API_NAME,
                AccountsReceivableDetailObjConstants.API_NAME,
                SFAPreDefineObject.Settlement.getApiName()
        ));
        /*List<IObjectDescribe> objectDescribeList = objectDescribeService.findDescribeListByApiNames(
                user.getTenantId(),
                Lists.newArrayList(
                        Utils.SALES_ORDER_API_NAME,
                        Utils.SALES_ORDER_PRODUCT_API_NAME,
                        Utils.ORDER_PAYMENT_API_NAME,
                        Utils.SALE_CONTRACT_API_NAME,
                        Utils.SALE_CONTRACT_LINE_API_NAME,
                        AccountsReceivableNoteObjConstants.API_NAME,
                        AccountsReceivableDetailObjConstants.API_NAME,
                        SFAPreDefineObject.Settlement.getApiName()
                )
        );*/
        boolean isOpenPaymentMultiSource = bizConfigThreadLocalCacheService.isOpenOrderPaymentMultiSource(user.getTenantId());
        boolean isOpenAR = bizConfigThreadLocalCacheService.isOpenAccountsReceivable(user.getTenantId());

        for (IObjectDescribe describe : describeApiNameMap.values()) {
            if (Objects.isNull(describe)) {
                continue;
            }
            String apiName = describe.getApiName();
            switch (apiName) {
                case Utils.SALES_ORDER_API_NAME:
                    addField2OtherObj(apiName, FIELD_JSON_SALES_ORDER, describe);
                    break;
                case Utils.SALES_ORDER_PRODUCT_API_NAME:
                    addField2OtherObj(apiName, FIELD_JSON_SALES_ORDER_PRODUCT, describe);
                    break;
                case Utils.ORDER_PAYMENT_API_NAME:
                    if (isOpenPaymentMultiSource) {
                        addField2OtherObj(apiName, SALE_CONTRACT_ID, describe);
                        addField2OtherObj(apiName, SALE_CONTRACT_LINE_ID, describe);
                    }
                    break;
                case Utils.SALE_CONTRACT_API_NAME:
                    if (isOpenPaymentMultiSource) {
                        addField2OtherObj(apiName, CONTRACT_RELATED_ORDER_PAYMENT_COUNT, describe);
                        jobScheduleService.submitCalculateJob(user, Lists.newArrayList("related_order_payment_count"), describe.getApiName());
                    }
                    if (isOpenAR) {
                        addField2OtherObj(apiName, SALE_CONTRACT_AR_TAG_AMOUNT, describe);
                        addField2OtherObj(apiName, SALE_CONTRACT_NO_AR_TAG_AMOUNT, describe);
                    }
                    break;
                case Utils.SALE_CONTRACT_LINE_API_NAME:
                    if (isOpenPaymentMultiSource) {
                        addField2OtherObj(apiName, CONTRACT_LINE_RELATED_ORDER_PAYMENT_COUNT, describe);
                        jobScheduleService.submitCalculateJob(user, Lists.newArrayList("related_order_payment_count"), describe.getApiName());
                    }
                    if (isOpenAR) {
                        addField2OtherObj(apiName, AR_TAG_AMOUNT, describe);
                        addField2OtherObj(apiName, NO_AR_TAG_AMOUNT, describe);
                    }
                    break;
                case AccountsReceivableNoteObjConstants.API_NAME:
                    if (isOpenAR) {
                        addField2OtherObj(apiName, SALE_CONTRACT_ID_FOR_AR, describe);
                    }
                    break;
                case AccountsReceivableDetailObjConstants.API_NAME:
                    if (isOpenAR) {
                        addField2OtherObj(apiName, SALE_CONTRACT_LINE_ID_FOR_AR, describe);
                    }
                    break;
                case "SettlementObj":
                    if (isOpenAR) {
                        addField2OtherObj(apiName, SALE_CONTRACT_ID_SETTLEMENT, describe);
                    }
                    break;
                default:
                    // 处理其他情况
                    break;
            }
        }
    }

    private void addField2OtherObj(String apiName, String fields, IObjectDescribe describe) throws MetadataServiceException {
        JSONObject jsonFiled = JSON.parseObject(fields);
        jsonFiled.keySet().stream().forEach(x -> generateNewField(apiName, describe, jsonFiled.getString(x)));
        objectDescribeService.update(describe);
    }

    private void generateNewField(String objDescribeApiName, IObjectDescribe describe, String fieldJson) {
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(fieldJson);
        fieldDescribe.setDescribeApiName(objDescribeApiName);
        if (describe.getFieldDescribe(fieldDescribe.getApiName()) == null) {
            describe.addFieldDescribe(fieldDescribe);
        }
    }

    private void init(User user, Map<String, IObjectDescribe> descMap, StopWatch stopWatch, List<String> apiNames) throws MetadataServiceException {
        try {
            initDescribeForTenant(user.getTenantId(), descMap, apiNames);
            stopWatch.lap("initDescribe");
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
            IObjectDescribe contractDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), Utils.SALE_CONTRACT_API_NAME);
            IObjectDescribe contractLinesDdescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), Utils.SALE_CONTRACT_LINE_API_NAME);
            descMap.put(Utils.SALE_CONTRACT_API_NAME, contractDescribe);
            descMap.put(Utils.SALE_CONTRACT_LINE_API_NAME, contractLinesDdescribe);
        }
        try {
            initMultiLayoutForOneTenant(apiNames, user.getTenantId());
            stopWatch.lap("initLayout");
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(), e);
        }

        boolean isOpenMulti = bizConfigThreadLocalCacheService.isMultipleUnit(user.getTenantId());
        boolean isSpuOpen = SFAConfigUtil.isSpuOpen(user.getTenantId());
        if (isOpenMulti) {
            List<ISelectOption> spuOptions = getSpuDesc(user.getTenantId(), isSpuOpen);
            List<String> saleContractsApiNames = Arrays.asList(Utils.SALE_CONTRACT_LINE_API_NAME);
            ModuleInitLayoutUtil.addFieldForXXDetail(user.getTenantId(), spuOptions, descMap.get(Utils.SALE_CONTRACT_LINE_API_NAME), saleContractsApiNames);
            ModuleInitLayoutUtil.addFormLayout(user.getTenantId(), saleContractsApiNames);
        }
    }

    private void initPrivilegeRelate(List<String> apiNames, User user, RequestContext context, String dataPermissionValue, String layoutApiName) {
        StringBuilder sb = new StringBuilder();
        String tenantId = user.getTenantId();
        apiNames.stream().forEach(descApiName -> {
            RequestContextManager.setContext(context);
            // 功能权限初始化
            try {
                functionPrivilegeService.initFunctionPrivilege(user, descApiName);
                sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" initFunctionPrivilege |||");
            } catch (Exception e) {
                sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" initFunctionPrivilege failed|||");
                log.error("initFunctionPrivilege failed:", e);
            }
            try {
                //record type 初始化
                recordTypeLogicService.recordTypeInit(user, layoutApiName, tenantId, descApiName);
                sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" recordTypeInit |||");
            } catch (Exception e) {
                log.error("recordTypeLogicService.recordTypeInit error", e);
                sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" recordTypeInit failed|||");
            }
            try {
                IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, descApiName);
                dataPrivilegeService.addCommonPrivilegeListResult(user, Lists.newArrayList(
                        new ObjectDataPermissionInfo(descApiName, describe.getDisplayName(), dataPermissionValue)));
                log.info("addCommonPrivilegeListResult called , dataPermissionValue is {}", dataPermissionValue);
                sb.append("||Success: Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult |||");
            } catch (Exception e) {
                log.error("dataPrivilegeService.addCommonPrivilegeListResult error", e);
                sb.append("Tenant:" + tenantId).append(",descApiName:" + descApiName).append(" addCommonPrivilegeListResult failed|||");
            }
        });
        log.info("initPrivilegeRelate executed:", sb.toString());
    }

    private void initMultiLayoutForOneTenant(List<String> apiNames, String tenantId) throws MetadataServiceException {
        for (String apiName : apiNames) {
            String detailLayoutStr = InitUtil.getLayoutJsonFromResourceByApiName(apiName, "detail");
            this.initLayoutByJson(tenantId, apiName, detailLayoutStr);
            String listLayoutStr = InitUtil.getLayoutJsonFromResourceByApiName(apiName, "list");
            this.initLayoutByJson(tenantId, apiName, listLayoutStr);

        }
    }

    private void initDescribeForTenant(String tenantId, Map<String, IObjectDescribe> descMap, List<String> apiNames) throws MetadataServiceException {
        for (String apiName : apiNames) {
            ObjectDescribe describe = InitUtil.getDescribeFromLocalResource(apiName);
            describe.setTenantId(tenantId);
            describe.setCreatedBy(SYSTEM_OPT_USER_ID);
            describe.setLastModifiedBy(SYSTEM_OPT_USER_ID);
            IObjectDescribe objectDescribe = objectDescribeService.create(describe, true, false);
            descMap.put(apiName, objectDescribe);
            log.info("objectDescribeDraftService.create executed! tenantId:{},apiName:{}", tenantId, apiName);
        }
    }

    private void initLayoutByJson(String tenantId, String describeApiName, String layoutJson) throws MetadataServiceException {
        Layout layout = new Layout();
        layout.fromJsonString(layoutJson);
        layout.setTenantId(tenantId);

        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(describeApiName, tenantId);

        if (CollectionUtils.isNotEmpty(layouts)) {
            Optional<ILayout> oldLayoutOpt = layouts.stream().filter(x -> layout.getLayoutType().equals(x.getLayoutType())).findFirst();
            if (oldLayoutOpt.isPresent()) {
                ILayout oldLayout = oldLayoutOpt.get();
                layout.setId(oldLayout.getId());
                layout.setVersion(oldLayout.getVersion());
                layoutService.replace(layout);
            } else {
                layoutService.create(layout);
            }
        } else {
            layoutService.create(layout);
        }
    }

    public List<ISelectOption> getSpuDesc(String tenantId, boolean isSpuOpen) throws MetadataServiceException {
        String apiName = isSpuOpen ? Utils.SPU_API_NAME : Utils.PRODUCT_API_NAME;
        IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        if (objectDescribe == null) {
            return Lists.newArrayList();
        }
        IFieldDescribe unitField = objectDescribe.getFieldDescribe("unit");
        if (unitField == null) {
            return Lists.newArrayList();
        }
        SelectOneFieldDescribe selectOneField = (SelectOneFieldDescribe) unitField;
        return selectOneField.getSelectOptions();
    }
}
