package com.facishare.crm.sfa.predefine.service.real.scanbarcode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PRODUCT_NONSTANDARD_ATTRIBUTE_NO_RELATIONSHIP;

public abstract class AbstractMultiUnitScanBarcodeService extends AbstractScanBarcodeService {
    private static final Set<Operator> operators = Collections.unmodifiableSet(Sets.newHashSet(Operator.EQ, Operator.IN, Operator.IS, Operator.HASANYOF));
    @Autowired
    protected ObjectDataServiceImpl objectDataService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    MultiUnitService multiUnitService;


    /**
     * 获取service的唯一标识
     */
    public String getId() {
        return HAVE_MULTI_UNIT_BARCODE_SCAN_KEY + getApiName();
    }

    /**
     * 获取产品字段的apiName
     *
     * @return
     */
    protected abstract String getProductFieldApiName();

    /**
     * 获取对象名
     *
     * @return
     */
    protected abstract String getApiName();

    /**
     * 计算多单位而的价格
     *
     * @param objectDataExt
     * @param conversionRatio
     */
    protected abstract void processPriceFields(ObjectDataExt objectDataExt, BigDecimal conversionRatio,BigDecimal pricingConversionRation);


    @Override
    public StandardRelatedListController.Result getScanBarcodeResult(StandardRelatedListController.Arg payload, RequestContext context) {
        List<String> barcodes = getBarcodeFilterValues(payload);

        StandardRelatedListController.Result scanBarcodeResult = super.getScanBarcodeResult(payload, context);

        processMultiUnitData(context.getUser(), scanBarcodeResult, payload.getSearchQueryInfo(), barcodes);
        return scanBarcodeResult;
    }

    @Override
    protected Controller generateController(StandardRelatedListController.Arg payload, RequestContext context) {
        Controller controller = super.generateController(payload, context);
        addSkuFilter2SearchQueryInfo(controller, context);
        return controller;
    }

    /**
     * 忽略排序条件为 单位 这种情况
     *
     * @param user
     * @param result
     * @param searchQueryStr query json string
     * @param barcodes
     */
    private void processMultiUnitData(User user, BaseListController.Result result, String searchQueryStr, List<String> barcodes) {
        List<ObjectDataDocument> dataList = result.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            String unitSqlPart = getUnitFiltersSqlPart(searchQueryStr);
            Map<Object, List<Map>> skuIdUnitInfoMap = getSkuMultiUnitInfo(user, unitSqlPart, barcodes, dataList);

            processMultiUnitData(user, result, dataList, skuIdUnitInfoMap);
        }
    }

    /**
     * 通过产品id列表,获取多单位信息,过滤多单位关联表的条形码
     *
     * @param user
     * @param unitSqlPart 单位条件的sql
     * @param barcodes    过滤条件中的条形码列表
     * @param dataList    过滤了数据权限的对象列表
     * @return 多单位信息
     */
    private Map<Object/*skuId*/, List<Map>> getSkuMultiUnitInfo(User user, String unitSqlPart, List<String> barcodes, List<ObjectDataDocument> dataList) {
        Map<String/*skuId*/, ObjectDataDocument> idDataMap = Maps.newHashMap();
        for (ObjectDataDocument pbp : dataList) {
            String skuId = (String) pbp.get(getProductFieldApiName());

            if (skuId != null) {
                idDataMap.put(skuId, pbp);
            }
        }
        Set<String> skuIds = idDataMap.keySet();
        String tmp = "select product_id, unit_id, conversion_ratio, u.name\n" +
                "from biz_multi_unit_related as r\n" +
                "       left join biz_unit_info as u on r.tenant_id = u.tenant_id and r.unit_id = u.id\n" +
                "where r.tenant_id = '%s'\n" +
                "  and r.is_deleted=0 \n" +
                "  and r.product_id in('%s')\n" +
                "  %s\n" +
                "  and r.barcode in('%s');";

        String sql = String.format(
                tmp,
                user.getTenantId(),
                ConcatenateSqlUtils.COLLECTION_JOINER.join(skuIds),
                unitSqlPart,
                ConcatenateSqlUtils.COLLECTION_JOINER.join(barcodes)
        );
        try {
            return objectDataService.findBySql(user.getTenantId(), sql).stream().collect(Collectors.groupingBy(o -> o.get("product_id")));
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * @param user
     * @param result
     * @param dataList
     * @param skuIdUnitInfoMap
     */
    private void processMultiUnitData(User user, BaseListController.Result result, List<ObjectDataDocument> dataList, Map<Object, List<Map>> skuIdUnitInfoMap) {
        if (MapUtils.isNotEmpty(skuIdUnitInfoMap)) {
            List<ObjectDataDocument> finalDataList = Lists.newArrayList();

            List<String> productIds = new ArrayList<>();
            List<IObjectData> multiUnitDatas = new ArrayList<>();
            for (ObjectDataDocument objectData : dataList) {
                if (getApiName().equals(Utils.PRICE_BOOK_PRODUCT_API_NAME)) {
                    productIds.add(objectData.get(getProductFieldApiName()).toString());
                }
            }
            if (CollectionUtils.isNotEmpty(productIds)) {
                multiUnitDatas = multiUnitService.getMultiUnitDataByProductIds(user, productIds);
            }

            int count = 0;
            for (ObjectDataDocument objectData : dataList) {
                List<Map> unitInfoList = skuIdUnitInfoMap.get(objectData.get(getProductFieldApiName()));
                if (unitInfoList != null) {
                    for (Map unitInfo : unitInfoList) {
                        String jsonString = JSON.toJSONString(objectData);
                        ObjectDataExt objectDataExt = ObjectDataExt.of(JSONObject.parseObject(jsonString));

                        processUnitField(user, result, unitInfo, objectDataExt);

                        /*BigDecimal conversionRatio = ((BigDecimal) unitInfo.get("conversion_ratio"));
                        BigDecimal pricingConversionRation = BigDecimal.ONE;
                        if (getApiName().equals(Utils.PRICE_BOOK_PRODUCT_API_NAME) && CollectionUtils.isNotEmpty(multiUnitDatas)) {
                            multiUnitDatas = multiUnitDatas.stream()
                                    .filter(x -> x.get("product_id", String.class).equals(objectData.get(getProductFieldApiName()).toString()))
                                    .filter(x -> x.get("is_pricing", Boolean.class))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(multiUnitDatas)) {
                                IObjectData multiUnit = multiUnitDatas.get(0);
                                pricingConversionRation = multiUnit.get("conversion_ratio", BigDecimal.class);
                            }
                        }
                        if (conversionRatio == null) {
                            //throw new ValidateException(String.format("productId %s 的多单位信息的转换比例为空", unitInfo.get("product_id")));
                            throw new ValidateException(I18N.text("sfa.product.multi.conversion.is.empty"
                                    , unitInfo.get("product_id")));
                        }*/

                        //processPriceFields(objectDataExt, conversionRatio, pricingConversionRation);
                        finalDataList.add(ObjectDataDocument.of(objectDataExt));
                        count++;
                    }
                } else {
                    finalDataList.add(objectData);
                    count++;
                }
            }
            result.setTotal(count);
            result.setDataList(finalDataList);
        }
    }

    /**
     * 处理数据的unit字段,如果是引用字段,需要翻译,如果不是unit直接插入unit_id就行
     *
     * @param user
     * @param result
     * @param unitInfo
     * @param objectDataExt
     */
    private void processUnitField(User user, BaseListController.Result result, Map unitInfo, ObjectDataExt objectDataExt) {
        ObjectDescribeDocument objectDescribe = result.getObjectDescribe();

        ObjectDescribeExt describeExt;
        if (objectDescribe != null) {
            describeExt = ObjectDescribeExt.of(objectDescribe);
        } else {
            IObjectDescribe object = describeLogicService.findObject(user.getTenantId(), objectDataExt.getDescribeApiName());
            describeExt = ObjectDescribeExt.of(object);
        }

        IFieldDescribe unit = describeExt.getFieldDescribe("unit");
        if (Objects.equals(unit.getType(), "quote")) {
            objectDataExt.set("unit", unitInfo.get("name"));
            objectDataExt.set("unit__v", unitInfo.get("unit_id"));
            objectDataExt.remove("unit__r");
        } else {
            objectDataExt.set("unit", unitInfo.get("unit_id"));
        }
    }


    private String getUnitFiltersSqlPart(String queryInfo) {
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(queryInfo);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);

        List<IFilter> filters = queryExt.getFilters();
        for (Wheres where : queryExt.getWheres()) {
            filters.addAll(where.getFilters());
        }

        StringBuilder unitSqlPart = new StringBuilder();
        for (IFilter filter : filters) {
            if (Objects.equals(filter.getFieldName(), "unit")) {
                if (operators.contains(filter.getOperator())) {
                    unitSqlPart.append(String.format(" and unit_id in('%s')", ConcatenateSqlUtils.COLLECTION_JOINER.join(filter.getFieldValues())));
                } else {
                    unitSqlPart.append(String.format(" and unit_id not in('%s')", ConcatenateSqlUtils.COLLECTION_JOINER.join(filter.getFieldValues())));
                }
            }
        }
        return unitSqlPart.toString();
    }

    private List<String> getBarcodeFilterValues(StandardRelatedListController.Arg payload) {
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(payload.getSearchQueryInfo());
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);

        List<IFilter> allFilters = Lists.newArrayList(queryExt.getFilters());

        for (Wheres where : queryExt.getWheres()) {
            allFilters.addAll(where.getFilters());
        }

        List<List<String>> barcodesList = Lists.newArrayList();
        for (IFilter filter : allFilters) {
            if (isScanBarcodeFilter(filter)) {
                List<String> barcodes = filter.getFieldValues();
                if (CollectionUtils.isEmpty(barcodes)) {
                    //throw new ValidateException("条形码为空");
                    throw new ValidateException(I18N.text("sfa.barcode.is.empty"));
                }
                barcodesList.add(barcodes);
                break;
            }
        }

        if (CollectionUtils.isEmpty(barcodesList)) {
            throw new ValidateException(I18N.text("sfa.barcode.is.empty"));
        }
        return barcodesList.get(0);
    }

    private void addSkuFilter2SearchQueryInfo(Controller controller, RequestContext context) {
        StandardRelatedListController.Arg arg = (StandardRelatedListController.Arg) controller.getArg();
        String searchQueryInfo = arg.getSearchQueryInfo();

        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);

        List<List<String>> barcodesList = Lists.newArrayList();

        List<IFilter> filters = queryExt.getFilters();
        fillBarcodesListAndRemoveBarcodes(filters, barcodesList);

        // wheres 的 filters toJsonString会有问题,用filters承载wheres
        for (Wheres where : query.getWheres()) {
            List<IFilter> whereFilters = Lists.newArrayList();
            for (Iterator<IFilter> filterIterator = where.getFilters().iterator(); filterIterator.hasNext(); ) {
                IFilter filter = filterIterator.next();
                if (isScanBarcodeFilter(filter) && barcodesList.size() == 0) {
                    List<String> barcodes = filter.getFieldValues();
                    barcodesList.add(barcodes);
                    filterIterator.remove();
                } else {
                    Filter filterCopy = SearchUtil.filter(filter.getFieldName(), filter.getOperator(), filter.getFieldValues());
                    whereFilters.add(filterCopy);
                }
            }
            where.setFilters(whereFilters);
        }

        addSkuFilter(context.getTenantId(), filters, barcodesList.get(0));
        arg.setSearchQueryInfo(queryExt.toSearchTemplateQuery().toJsonString());
    }

    /**
     * 判断是否是 扫码的条形码条件
     * <p>
     * 如果默认配置的条件也是barcode且也是eq连接,选取任何一个条件都可以.
     * 因为,如果value不同,返回值一定为空,如果value相同,那么使用哪个都一样
     *
     * @param filter
     * @return filter.name为barcode且操作条件为eq, 认定为扫码的条件
     */
    private boolean isScanBarcodeFilter(IFilter filter) {
        return "barcode".equals(filter.getFieldName()) && Objects.equals(Operator.EQ, filter.getOperator());
    }

    private void fillBarcodesListAndRemoveBarcodes(List<IFilter> filterList, List<List<String>> barcodesList) {
        Iterator<IFilter> filterIterator = filterList.iterator();

        while (filterIterator.hasNext()) {
            IFilter filter = filterIterator.next();
            if (isScanBarcodeFilter(filter)) {
                List<String> barcodes = filter.getFieldValues();
                barcodesList.add(barcodes);
                filterIterator.remove();
                break;
            }
        }
    }

    /**
     * @param tenantId
     * @param filters
     * @param barcodes
     */
    private void addSkuFilter(String tenantId, List<IFilter> filters, List<String> barcodes) {
        String tmpSql = "(select id\n" +
                " from biz_product\n" +
                " where tenant_id = '%s'\n" +
                "   and barcode in ('%s')\n" +
                "   and is_deleted = 0) union (select distinct product_id\n" +
                "                              from biz_multi_unit_related\n" +
                "                              where tenant_id = '%s'\n" +
                "                                and barcode in ('%s')\n" +
                "                                and is_deleted = 0\n" +
                "                                and product_id is not null)";

        String join = ConcatenateSqlUtils.COLLECTION_JOINER.join(barcodes);
        String sql = String.format(tmpSql, tenantId, join, tenantId, join);
        try {
            List<String> skuIds = objectDataService.findBySql(tenantId, sql).stream().map(o -> (String) o.get("id")).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(skuIds)) {
                SearchUtil.fillFilterIn(filters, getProductFieldApiName(), skuIds);
            } else {
                SearchUtil.fillFilterIn(filters, "barcode", barcodes);
            }
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
    }
}
